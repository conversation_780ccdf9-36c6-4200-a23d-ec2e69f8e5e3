# 基于区块链的碳排放核查系统

[![Python](https://img.shields.io/badge/Python-3.8+-blue.svg)](https://www.python.org/)
[![Flask](https://img.shields.io/badge/Flask-3.0.0-green.svg)](https://flask.palletsprojects.com/)
[![MySQL](https://img.shields.io/badge/MySQL-8.0+-orange.svg)](https://www.mysql.com/)
[![Ethereum](https://img.shields.io/badge/Ethereum-Solidity-purple.svg)](https://ethereum.org/)
[![License](https://img.shields.io/badge/License-MIT-yellow.svg)](LICENSE)

> 一个基于区块链技术的碳排放核查系统，旨在通过透明、可信的方式管理企业碳排放数据，支持核查、交易和分析功能。

## 📋 项目概述

本系统是一个完整的碳排放管理解决方案，采用区块链技术确保数据的不可篡改性和透明性。系统支持企业排放数据提交、第三方核查、碳配额交易、数据分析和报告生成等核心功能。

### 🎯 核心特性

- **🔐 区块链集成**：关键数据上链存储，确保数据不可篡改
- **👥 多角色管理**：支持管理员、企业用户、核查机构三种角色
- **📊 数据管理**：完整的排放数据生命周期管理
- **✅ 核查流程**：标准化的第三方核查流程
- **💰 碳交易**：企业间碳配额交易功能
- **📈 数据分析**：排放趋势分析和预测
- **📄 报告生成**：自动化报告生成和导出
- **🎨 绿色UI**：环保主题的用户界面设计

### 🛠️ 技术架构

- **后端框架**：Flask 3.0.0 (Python)
- **前端技术**：HTML5, CSS3, JavaScript, Bootstrap
- **数据库**：MySQL 8.0+
- **区块链**：以太坊 (Solidity智能合约)
- **Web3集成**：Web3.py
- **认证方式**：基于Session的认证
- **API设计**：RESTful API
- **开发工具**：Ganache (本地区块链)

## 🏗️ 系统架构

### 核心模块

#### 1. 👤 用户管理模块
- **多角色支持**：管理员、企业用户、核查机构
- **权限控制**：基于角色的访问控制
- **用户认证**：安全的登录和会话管理

#### 2. 📊 排放数据管理模块
- **数据提交**：企业用户提交排放数据
- **数据验证**：自动化数据校验和格式检查
- **状态管理**：pending → verified → rejected 状态流转
- **区块链存储**：关键数据自动上链

#### 3. ✅ 核查管理模块
- **任务分配**：自动分配核查任务给核查机构
- **核查流程**：标准化的核查工作流
- **结果记录**：核查意见和结论记录
- **区块链验证**：核查结果上链存储

#### 4. 💰 碳交易模块
- **配额管理**：企业碳排放配额管理
- **交易创建**：企业间配额交易
- **交易执行**：自动化交易处理
- **交易记录**：完整的交易历史追踪

#### 5. 🧮 碳计算器模块
- **多源计算**：支持燃煤、天然气、电力等多种排放源
- **标准因子**：内置国际标准排放因子
- **自定义计算**：支持自定义排放因子
- **结果保存**：计算结果可保存为排放数据

#### 6. 📈 预测分析模块
- **趋势分析**：基于历史数据的排放趋势分析
- **预测模型**：机器学习算法预测未来排放
- **可视化展示**：图表化展示分析结果

#### 7. 📄 报告生成模块
- **多种模板**：排放报告、核查报告、交易报告
- **自动生成**：基于数据自动生成报告
- **格式支持**：HTML、PDF格式导出
- **统一风格**：绿色环保主题设计

#### 8. ⛓️ 区块链模块
- **智能合约**：Solidity编写的碳排放管理合约
- **数据上链**：排放数据、核查记录、交易记录上链
- **数据验证**：区块链数据完整性验证
- **模拟模式**：支持无区块链环境下的模拟运行

## 🚀 快速开始

### 📋 环境要求

| 组件 | 版本要求 | 说明 |
|------|----------|------|
| Python | 3.8+ | 后端运行环境 |
| MySQL | 8.0+ | 数据库 |
| Ganache | 最新版 | 本地区块链（可选） |
| 浏览器 | 现代浏览器 | Chrome, Firefox, Edge等 |

### ⚡ 一键启动

```bash
# 1. 安装依赖
pip install -r requirements.txt

# 2. 初始化数据库
python init_db.py

# 3. 生成演示数据
python generate_demo_data.py

# 4. 启动系统
python run.py
```

### 📖 详细安装步骤

#### 1. 克隆项目
```bash
git clone <repository-url>
cd CMS
```

#### 2. 安装Python依赖
```bash
pip install -r requirements.txt
```

#### 3. 配置数据库
确保MySQL服务正在运行，系统将连接到预配置的远程数据库。

#### 4. 初始化数据库
```bash
python init_db.py
```
此命令将：
- 测试数据库连接
- 创建所有必需的数据表
- 验证表结构完整性

#### 5. 生成演示数据（推荐）
```bash
python generate_demo_data.py
```
此命令将生成：
- 5个测试用户（3个企业 + 2个核查机构）
- 企业碳配额数据
- 排放数据样本
- 核查记录
- 交易记录

#### 6. 启动系统
```bash
python run.py
```
系统将在 `http://localhost:5000` 启动

## ⛓️ 区块链配置

系统支持两种运行模式：**区块链模式** 和 **模拟模式**。

### 🔧 区块链模式配置

#### 1. 安装Ganache
下载并安装 [Ganache](https://www.trufflesuite.com/ganache)

#### 2. 启动Ganache
- 启动Ganache，创建新的工作区
- 记录RPC服务器URL（默认：`http://127.0.0.1:8545`）
- 记录生成的10个测试账户地址和私钥

#### 3. 配置环境变量
编辑 `.env` 文件，配置区块链相关参数：
```env
# 区块链配置
ETHEREUM_NODE_URL=http://127.0.0.1:8545
ADMIN_ADDRESS=0x...
ADMIN_PRIVATE_KEY=0x...
ENTERPRISE_1_ADDRESS=0x...
ENTERPRISE_1_KEY=0x...
# ... 其他账户配置
```

#### 4. 部署智能合约
```bash
python blockchain/deploy_contract.py
```

#### 5. 验证区块链功能
- 企业提交排放数据 → 数据上链
- 核查机构核查数据 → 核查结果上链
- 企业间交易配额 → 交易记录上链
- 管理员执行惩罚 → 惩罚记录上链

### 🎭 模拟模式
如果没有配置区块链环境，系统将自动启用模拟模式：
- 所有区块链操作将被模拟
- 数据仍然保存在数据库中
- 系统功能完全可用
- 适合演示和测试

## 📁 项目结构

```
CMS/
├── 📂 backend/                    # 后端核心代码
│   ├── 🐍 __init__.py            # Flask应用初始化
│   ├── 📂 models/                # 数据库模型
│   │   ├── user.py              # 用户模型
│   │   ├── emission.py          # 排放数据模型
│   │   ├── verification.py      # 核查记录模型
│   │   ├── carbon_quota.py      # 碳配额模型
│   │   ├── transaction.py       # 交易记录模型
│   │   └── ...                  # 其他模型
│   ├── 📂 routes/                # API路由
│   │   ├── auth.py              # 用户认证
│   │   ├── admin.py             # 管理员功能
│   │   ├── emission.py          # 排放数据管理
│   │   ├── verification.py      # 核查管理
│   │   ├── transaction.py       # 交易管理
│   │   ├── calculator.py        # 碳计算器
│   │   ├── prediction.py        # 预测分析
│   │   ├── report.py            # 报告生成
│   │   └── blockchain.py        # 区块链接口
│   ├── 📂 utils/                 # 工具类
│   │   ├── carbon_calculator.py # 碳排放计算
│   │   ├── prediction.py        # 预测算法
│   │   └── report_generator.py  # 报告生成
│   └── 📂 blockchain/            # 区块链客户端
│       ├── client.py            # Web3客户端
│       └── event_listener.py    # 事件监听
│
├── 📂 blockchain/                 # 智能合约
│   ├── 📂 contracts/             # Solidity合约
│   │   └── CarbonEmission.sol   # 碳排放管理合约
│   └── deploy_contract.py       # 合约部署脚本
│
├── 📂 templates/                  # HTML模板
│   ├── 📂 admin/                 # 管理员页面
│   ├── 📂 enterprise/            # 企业页面
│   ├── 📂 verifier/              # 核查机构页面
│   └── 📂 reports/               # 报告模板
│
├── 📂 static/                     # 静态资源
│   ├── 📂 css/                   # 样式文件
│   ├── 📂 js/                    # JavaScript文件
│   └── 📂 images/                # 图片资源
│
├── 📂 frontend/                   # React前端（可选）
│   ├── 📂 src/                   # 源代码
│   └── package.json             # 前端配置
│
├── 📂 tests/                      # 测试代码
│   ├── init_test_data.py        # 测试数据
│   └── ...                      # 其他测试
│
├── 🔧 .env                       # 环境变量配置
├── 🗄️ create_tables.sql          # 数据库表结构
├── 🐍 init_db.py                 # 数据库初始化
├── 🐍 generate_demo_data.py      # 演示数据生成
├── 🐍 run.py                     # 应用启动脚本
├── 📋 requirements.txt           # Python依赖
├── 📖 README.md                  # 项目说明
└── 📖 DEMO_GUIDE.md              # 演示指南
```

## 🎮 系统演示

### 👥 测试账户

| 角色 | 用户名 | 密码 | 说明 |
|------|--------|------|------|
| 管理员 | admin | admin123 | 系统管理员 |
| 企业用户 | enterprise1 | password123 | 北京碳排放科技有限公司 |
| 企业用户 | enterprise2 | password123 | 上海绿色能源有限公司 |
| 企业用户 | enterprise3 | password123 | 广州环保科技有限公司 |
| 核查机构 | verifier1 | password123 | 国家碳排放核查中心 |
| 核查机构 | verifier2 | password123 | 碳核查认证机构 |

### 🎯 演示流程

#### 1️⃣ 企业用户演示
```
登录 → 查看仪表板 → 提交排放数据 → 使用碳计算器 → 查看交易记录 → 生成报告
```

#### 2️⃣ 核查机构演示
```
登录 → 查看核查任务 → 执行核查 → 提交核查结果 → 查看核查历史
```

#### 3️⃣ 管理员演示
```
登录 → 查看系统概览 → 管理用户 → 配置区块链 → 查看系统统计
```

### 📊 演示数据

系统包含以下演示数据：
- **用户数据**：5个测试用户
- **排放数据**：10条排放记录
- **核查记录**：5条核查结果
- **交易记录**：6条碳交易
- **碳配额**：每个企业的年度配额

### 🌐 访问地址

- **系统首页**：http://localhost:5000
- **登录页面**：http://localhost:5000/login
- **管理员后台**：http://localhost:5000/admin/dashboard
- **企业工作台**：http://localhost:5000/enterprise/dashboard
- **核查机构**：http://localhost:5000/verifier/dashboard

## 2025年系统更新

2025年版本的碳排放管理系统进行了以下更新：

1. **UI界面升级**：
   - 采用统一的绿色环保风格
   - 优化用户界面，提升用户体验
   - 增加响应式设计，适配不同设备

2. **功能增强**：
   - 改进报告生成模块，增加更多报告模板
   - 优化核查流程，提高核查效率
   - 增强数据可视化功能，提供更直观的数据展示

3. **性能优化**：
   - 提高系统响应速度
   - 优化数据库查询性能
   - 减少资源占用

4. **安全增强**：
   - 加强数据安全保护
   - 完善用户权限管理
   - 增加操作日志记录

## 系统特色

1. **绿色环保设计**：
   - 系统采用绿色环保风格设计，符合碳排放管理的主题
   - 使用渐变色、圆角设计和阴影效果，提升视觉体验
   - 统一的色彩方案和设计元素，保持系统风格一致性

2. **数据可视化**：
   - 使用SVG图表展示排放数据和趋势
   - 提供多种图表类型（柱状图、折线图、饼图等）
   - 支持数据筛选和导出

3. **报告模板**：
   - 提供多种专业报告模板
   - 支持自定义报告内容和格式
   - 生成美观、专业的PDF和HTML报告

4. **区块链集成**：
   - 将关键数据记录到区块链，确保数据不可篡改
   - 提供数据验证和溯源功能
   - 增强系统的可信度和透明度

## 创新点

1. **区块链技术应用**：
   - 将区块链技术应用于碳排放管理
   - 保证数据的不可篡改性和透明性
   - 提高系统的可信度和安全性

2. **碳计算器功能**：
   - 提供多种活动的碳排放计算
   - 支持自定义排放因子
   - 生成详细的计算报告

3. **预测分析功能**：
   - 使用机器学习算法预测未来排放
   - 提供多种预测模型
   - 支持模型训练和评估

4. **自动化报告生成**：
   - 自动生成各类报告
   - 支持多种报告格式（PDF、HTML等）
   - 提高工作效率

## 未来展望

1. **功能扩展**：
   - 增加碳足迹追踪功能
   - 添加碳中和规划工具
   - 集成更多数据源和分析工具

2. **技术升级**：
   - 引入人工智能技术，提升预测精度
   - 优化区块链集成，提高性能和可扩展性
   - 增强系统安全性和稳定性

3. **生态建设**：
   - 构建碳排放管理生态系统
   - 支持第三方插件和扩展
   - 促进行业合作和数据共享

## 系统局限性与未来改进

1. **当前局限性**：
   - 区块链集成仅支持本地Ganache节点
   - 预测模型精度有限
   - 用户界面可进一步优化

2. **未来改进方向**：
   - 支持连接到公共以太坊测试网络（如Rinkeby、Goerli等）
   - 优化智能合约，减少Gas消耗
   - 增加区块链浏览器功能，方便查看链上数据
   - 优化预测模型，提高预测精度
   - 改进用户界面，提升用户体验
   - 添加更多的数据可视化功能
   - 实现移动端适配

## 作者信息

- **姓名**：XXX
- **学号**：XXX
- **指导教师**：XXX
- **学院**：XXX学院

## 版权声明

© 2025 碳排放管理系统 版权所有