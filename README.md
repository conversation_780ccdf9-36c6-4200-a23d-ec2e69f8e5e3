# 基于区块链的碳排放核查系统

[![Python](https://img.shields.io/badge/Python-3.8+-blue.svg)](https://www.python.org/)
[![Flask](https://img.shields.io/badge/Flask-3.0.0-green.svg)](https://flask.palletsprojects.com/)
[![MySQL](https://img.shields.io/badge/MySQL-8.0+-orange.svg)](https://www.mysql.com/)
[![Ethereum](https://img.shields.io/badge/Ethereum-Solidity-purple.svg)](https://ethereum.org/)

> 一个基于区块链技术的碳排放核查系统，旨在通过透明、可信的方式管理企业碳排放数据，支持核查、交易和分析功能。

## 📋 项目概述

本系统是一个完整的碳排放管理解决方案，采用区块链技术确保数据的不可篡改性和透明性。系统支持企业排放数据提交、第三方核查、碳配额交易、数据分析和报告生成等核心功能。

### 🎯 核心特性

- **🔐 区块链集成**：关键数据上链存储，确保数据不可篡改
- **👥 多角色管理**：支持管理员、企业用户、核查机构三种角色
- **📊 数据管理**：完整的排放数据生命周期管理
- **✅ 核查流程**：标准化的第三方核查流程
- **💰 碳交易**：企业间碳配额交易功能
- **📈 数据分析**：排放趋势分析和预测
- **📄 报告生成**：自动化报告生成和导出
- **🎨 绿色UI**：环保主题的用户界面设计

### 🛠️ 技术架构

- **后端框架**：Flask 3.0.0 (Python)
- **前端技术**：HTML5, CSS3, JavaScript, Bootstrap
- **数据库**：MySQL 8.0+
- **区块链**：以太坊 (Solidity智能合约)
- **Web3集成**：Web3.py
- **认证方式**：基于Session的认证
- **API设计**：RESTful API
- **开发工具**：Ganache (本地区块链)

## 🏗️ 系统架构

### 核心模块

#### 1. 👤 用户管理模块
- **多角色支持**：管理员、企业用户、核查机构
- **权限控制**：基于角色的访问控制
- **用户认证**：安全的登录和会话管理

#### 2. 📊 排放数据管理模块
- **数据提交**：企业用户提交排放数据
- **数据验证**：自动化数据校验和格式检查
- **状态管理**：pending → verified → rejected 状态流转
- **区块链存储**：关键数据自动上链

#### 3. ✅ 核查管理模块
- **任务分配**：自动分配核查任务给核查机构
- **核查流程**：标准化的核查工作流
- **结果记录**：核查意见和结论记录
- **区块链验证**：核查结果上链存储

#### 4. 💰 碳交易模块
- **配额管理**：企业碳排放配额管理
- **交易创建**：企业间配额交易
- **交易执行**：自动化交易处理
- **交易记录**：完整的交易历史追踪

#### 5. 🧮 碳计算器模块
- **多源计算**：支持燃煤、天然气、电力等多种排放源
- **标准因子**：内置国际标准排放因子
- **自定义计算**：支持自定义排放因子
- **结果保存**：计算结果可保存为排放数据

#### 6. 📈 预测分析模块
- **趋势分析**：基于历史数据的排放趋势分析
- **预测模型**：机器学习算法预测未来排放
- **可视化展示**：图表化展示分析结果

#### 7. 📄 报告生成模块
- **多种模板**：排放报告、核查报告、交易报告
- **自动生成**：基于数据自动生成报告
- **格式支持**：HTML、PDF格式导出
- **统一风格**：绿色环保主题设计

#### 8. ⛓️ 区块链模块
- **智能合约**：Solidity编写的碳排放管理合约
- **数据上链**：排放数据、核查记录、交易记录上链
- **数据验证**：区块链数据完整性验证
- **模拟模式**：支持无区块链环境下的模拟运行

## 🚀 快速开始

### 📋 环境要求

| 组件 | 版本要求 | 说明 |
|------|----------|------|
| Python | 3.8+ | 后端运行环境 |
| MySQL | 8.0+ | 数据库 |
| Ganache | 最新版 | 本地区块链（可选） |
| 浏览器 | 现代浏览器 | Chrome, Firefox, Edge等 |

### ⚡ 一键启动

```bash
# 1. 安装依赖
pip install -r requirements.txt

# 2. 初始化数据库
python init_db.py

# 3. 生成演示数据
python generate_demo_data.py

# 4. 启动系统
python run.py
```

### 📖 详细安装步骤

#### 1. 克隆项目
```bash
git clone <repository-url>
cd CMS
```

#### 2. 安装Python依赖
```bash
pip install -r requirements.txt
```

#### 3. 配置数据库
确保MySQL服务正在运行，系统将连接到预配置的远程数据库。

#### 4. 初始化数据库
```bash
python init_db.py
```
此命令将：
- 测试数据库连接
- 创建所有必需的数据表
- 验证表结构完整性

#### 5. 生成演示数据（推荐）
```bash
python generate_demo_data.py
```
此命令将生成：
- 5个测试用户（3个企业 + 2个核查机构）
- 企业碳配额数据
- 排放数据样本
- 核查记录
- 交易记录

#### 6. 启动系统
```bash
python run.py
```
系统将在 `http://localhost:5000` 启动

## ⛓️ 区块链配置

系统支持两种运行模式：**区块链模式** 和 **模拟模式**。

### 🔧 区块链模式配置

#### 1. 安装Ganache
下载并安装 [Ganache](https://www.trufflesuite.com/ganache)

#### 2. 启动Ganache
- 启动Ganache，创建新的工作区
- 记录RPC服务器URL（默认：`http://127.0.0.1:8545`）
- 记录生成的10个测试账户地址和私钥

#### 3. 配置环境变量
编辑 `.env` 文件，配置区块链相关参数：
```env
# 区块链配置
ETHEREUM_NODE_URL=http://127.0.0.1:8545
ADMIN_ADDRESS=0x...
ADMIN_PRIVATE_KEY=0x...
ENTERPRISE_1_ADDRESS=0x...
ENTERPRISE_1_KEY=0x...
# ... 其他账户配置
```

#### 4. 部署智能合约
```bash
python blockchain/deploy_contract.py
```

#### 5. 验证区块链功能
- 企业提交排放数据 → 数据上链
- 核查机构核查数据 → 核查结果上链
- 企业间交易配额 → 交易记录上链
- 管理员执行惩罚 → 惩罚记录上链

### 🎭 模拟模式
如果没有配置区块链环境，系统将自动启用模拟模式：
- 所有区块链操作将被模拟
- 数据仍然保存在数据库中
- 系统功能完全可用
- 适合演示和测试

## 📁 项目结构

```
CMS/
├── 📂 backend/                    # 后端核心代码
│   ├── 🐍 __init__.py            # Flask应用初始化
│   ├── 📂 models/                # 数据库模型
│   │   ├── user.py              # 用户模型
│   │   ├── emission.py          # 排放数据模型
│   │   ├── verification.py      # 核查记录模型
│   │   ├── carbon_quota.py      # 碳配额模型
│   │   ├── transaction.py       # 交易记录模型
│   │   └── ...                  # 其他模型
│   ├── 📂 routes/                # API路由
│   │   ├── auth.py              # 用户认证
│   │   ├── admin.py             # 管理员功能
│   │   ├── emission.py          # 排放数据管理
│   │   ├── verification.py      # 核查管理
│   │   ├── transaction.py       # 交易管理
│   │   ├── calculator.py        # 碳计算器
│   │   ├── prediction.py        # 预测分析
│   │   ├── report.py            # 报告生成
│   │   └── blockchain.py        # 区块链接口
│   ├── 📂 utils/                 # 工具类
│   │   ├── carbon_calculator.py # 碳排放计算
│   │   ├── prediction.py        # 预测算法
│   │   └── report_generator.py  # 报告生成
│   └── 📂 blockchain/            # 区块链客户端
│       ├── client.py            # Web3客户端
│       └── event_listener.py    # 事件监听
│
├── 📂 blockchain/                 # 智能合约
│   ├── 📂 contracts/             # Solidity合约
│   │   └── CarbonEmission.sol   # 碳排放管理合约
│   └── deploy_contract.py       # 合约部署脚本
│
├── 📂 templates/                  # HTML模板
│   ├── 📂 admin/                 # 管理员页面
│   ├── 📂 enterprise/            # 企业页面
│   ├── 📂 verifier/              # 核查机构页面
│   └── 📂 reports/               # 报告模板
│
├── 📂 static/                     # 静态资源
│   ├── 📂 css/                   # 样式文件
│   ├── 📂 js/                    # JavaScript文件
│   └── 📂 images/                # 图片资源
│
├── 📂 frontend/                   # React前端（可选）
│   ├── 📂 src/                   # 源代码
│   └── package.json             # 前端配置
│
├── 📂 tests/                      # 测试代码
│   ├── init_test_data.py        # 测试数据
│   └── ...                      # 其他测试
│
├── 🔧 .env                       # 环境变量配置
├── 🗄️ create_tables.sql          # 数据库表结构
├── 🐍 init_db.py                 # 数据库初始化
├── 🐍 generate_demo_data.py      # 演示数据生成
├── 🐍 run.py                     # 应用启动脚本
├── 📋 requirements.txt           # Python依赖
├── 📖 README.md                  # 项目说明
└── 📖 DEMO_GUIDE.md              # 演示指南
```

## 🎮 系统演示

### 👥 测试账户

| 角色 | 用户名 | 密码 | 说明 |
|------|--------|------|------|
| 管理员 | admin | admin123 | 系统管理员 |
| 企业用户 | enterprise1 | password123 | 北京碳排放科技有限公司 |
| 企业用户 | enterprise2 | password123 | 上海绿色能源有限公司 |
| 企业用户 | enterprise3 | password123 | 广州环保科技有限公司 |
| 核查机构 | verifier1 | password123 | 国家碳排放核查中心 |
| 核查机构 | verifier2 | password123 | 碳核查认证机构 |

### 🎯 演示流程

#### 1️⃣ 企业用户演示
```
登录 → 查看仪表板 → 提交排放数据 → 使用碳计算器 → 查看交易记录 → 生成报告
```

#### 2️⃣ 核查机构演示
```
登录 → 查看核查任务 → 执行核查 → 提交核查结果 → 查看核查历史
```

#### 3️⃣ 管理员演示
```
登录 → 查看系统概览 → 管理用户 → 配置区块链 → 查看系统统计
```

### 📊 演示数据

系统包含以下演示数据：
- **用户数据**：5个测试用户
- **排放数据**：10条排放记录
- **核查记录**：5条核查结果
- **交易记录**：6条碳交易
- **碳配额**：每个企业的年度配额

### 🌐 访问地址

- **系统首页**：http://localhost:5000
- **登录页面**：http://localhost:5000/login
- **管理员后台**：http://localhost:5000/admin/dashboard
- **企业工作台**：http://localhost:5000/enterprise/dashboard
- **核查机构**：http://localhost:5000/verifier/dashboard

## 💡 系统特色

### 🌟 创新点

1. **区块链技术应用**
   - 将区块链技术应用于碳排放管理领域
   - 确保数据的不可篡改性和透明性
   - 提高系统的可信度和安全性

2. **多角色协同工作**
   - 企业、核查机构、管理员三方协同
   - 完整的核查工作流程
   - 透明的数据共享机制

3. **智能化分析**
   - 碳排放趋势分析
   - 机器学习预测模型
   - 自动化报告生成

4. **绿色环保设计**
   - 统一的绿色环保UI风格
   - 符合碳排放管理主题
   - 优秀的用户体验设计

## 📊 技术实现

### 数据库设计
- **用户表**：存储用户信息和角色权限
- **排放数据表**：记录企业排放数据
- **核查记录表**：存储核查结果和意见
- **交易记录表**：记录碳配额交易
- **碳配额表**：管理企业年度配额
- **惩罚记录表**：记录违规惩罚

### 智能合约设计
```solidity
contract CarbonEmission {
    // 排放数据结构
    struct EmissionData { ... }

    // 核查记录结构
    struct VerificationRecord { ... }

    // 交易记录结构
    struct Transaction { ... }

    // 核心功能函数
    function submitEmissionData(...) public { ... }
    function submitVerification(...) public { ... }
    function createTransaction(...) public { ... }
}
```

### API接口设计
- **认证接口**：用户登录、注册、权限验证
- **数据管理接口**：排放数据CRUD操作
- **核查接口**：核查任务分配和结果提交
- **交易接口**：碳配额交易管理
- **报告接口**：报告生成和下载
- **区块链接口**：区块链数据同步

## 🔧 开发指南

### 开发环境搭建
```bash
# 1. 创建虚拟环境
python -m venv venv
source venv/bin/activate  # Linux/Mac
venv\Scripts\activate     # Windows

# 2. 安装开发依赖
pip install -r requirements.txt

# 3. 配置开发数据库
python init_db.py

# 4. 启动开发服务器
python run.py
```

### 代码规范
- 遵循PEP 8 Python代码规范
- 使用有意义的变量和函数名
- 添加必要的注释和文档字符串
- 进行单元测试和集成测试

### 贡献指南
1. Fork 项目仓库
2. 创建功能分支
3. 提交代码更改
4. 创建 Pull Request
5. 代码审查和合并

## 📝 许可证

本项目采用 MIT 许可证 - 查看 [LICENSE](LICENSE) 文件了解详情

## 🤝 致谢

感谢所有为本项目做出贡献的开发者和用户。

## 📞 联系方式

如有问题或建议，请通过以下方式联系：

- 📧 邮箱：[<EMAIL>]
- 🐛 问题反馈：[GitHub Issues]
- 📖 文档：[项目文档链接]

---

**⭐ 如果这个项目对您有帮助，请给我们一个星标！**