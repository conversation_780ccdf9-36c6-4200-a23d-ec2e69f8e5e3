#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
数据库初始化脚本
使用SQLAlchemy初始化数据库表结构
"""

import pymysql
from backend import create_app, db

def test_connection():
    """测试数据库连接"""
    try:
        connection = pymysql.connect(
            host='***********',
            port=3306,
            user='wuhong',
            password='D7mH8rZ7a7Z2kJa8',
            database='ces',
            charset='utf8mb4'
        )
        connection.close()
        print('数据库连接测试成功')
        return True
    except Exception as e:
        print(f'数据库连接测试失败: {e}')
        return False

def init_db_with_sqlalchemy():
    """使用SQLAlchemy初始化数据库"""
    try:
        app = create_app()
        with app.app_context():
            # 删除所有表（如果存在）
            print('删除现有表...')
            db.drop_all()

            # 创建所有表
            print('创建数据库表...')
            db.create_all()

            print('数据库表创建成功')
            return True
    except Exception as e:
        print(f'SQLAlchemy初始化失败: {e}')
        return False

def verify_database():
    """验证数据库表是否创建成功"""
    try:
        connection = pymysql.connect(
            host='***********',
            port=3306,
            user='wuhong',
            password='D7mH8rZ7a7Z2kJa8',
            database='ces',
            charset='utf8mb4'
        )

        cursor = connection.cursor()
        cursor.execute("SHOW TABLES")
        tables = cursor.fetchall()

        expected_tables = [
            'user', 'emission_data', 'verification',
            'transaction', 'carbon_quota', 'penalty'
        ]

        existing_tables = [table[0] for table in tables]
        print(f'现有表: {existing_tables}')

        missing_tables = []
        for table in expected_tables:
            if table not in existing_tables:
                missing_tables.append(table)

        if missing_tables:
            print(f'缺少表: {missing_tables}')
            return False
        else:
            print('所有必需的表都已创建')
            return True

    except Exception as e:
        print(f'验证数据库失败: {e}')
        return False
    finally:
        if 'connection' in locals():
            connection.close()

def init_db():
    """初始化数据库"""
    print('=' * 50)
    print('数据库初始化脚本')
    print('=' * 50)

    # 测试数据库连接
    print('1. 测试数据库连接...')
    if not test_connection():
        print('数据库连接失败，无法初始化数据库')
        return False

    # 使用SQLAlchemy初始化
    print('2. 使用SQLAlchemy初始化数据库...')
    if not init_db_with_sqlalchemy():
        print('SQLAlchemy初始化失败')
        return False

    # 验证数据库
    print('3. 验证数据库表...')
    if verify_database():
        print('=' * 50)
        print('数据库初始化成功！')
        print('=' * 50)
        print('下一步：运行 python generate_demo_data.py 生成演示数据')
        return True
    else:
        print('数据库初始化失败')
        return False

if __name__ == '__main__':
    init_db()
