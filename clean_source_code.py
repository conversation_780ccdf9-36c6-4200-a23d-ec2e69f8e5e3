#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
源代码清理脚本
用于清理不必要的文件，准备提交版本
"""

import os
import shutil
import glob
from pathlib import Path

def clean_python_cache():
    """清理Python缓存文件"""
    print("正在清理Python缓存文件...")
    
    # 清理__pycache__目录
    for pycache_dir in glob.glob("**/__pycache__", recursive=True):
        print(f"删除: {pycache_dir}")
        shutil.rmtree(pycache_dir, ignore_errors=True)
    
    # 清理.pyc文件
    for pyc_file in glob.glob("**/*.pyc", recursive=True):
        print(f"删除: {pyc_file}")
        os.remove(pyc_file)
    
    # 清理.pyo文件
    for pyo_file in glob.glob("**/*.pyo", recursive=True):
        print(f"删除: {pyo_file}")
        os.remove(pyo_file)

def clean_node_modules():
    """清理Node.js模块"""
    print("正在清理Node.js模块...")
    
    for node_modules in glob.glob("**/node_modules", recursive=True):
        print(f"删除: {node_modules}")
        shutil.rmtree(node_modules, ignore_errors=True)

def clean_build_files():
    """清理构建文件"""
    print("正在清理构建文件...")
    
    # 清理dist目录
    for dist_dir in glob.glob("**/dist", recursive=True):
        print(f"删除: {dist_dir}")
        shutil.rmtree(dist_dir, ignore_errors=True)
    
    # 清理build目录
    for build_dir in glob.glob("**/build", recursive=True):
        print(f"删除: {build_dir}")
        shutil.rmtree(build_dir, ignore_errors=True)

def clean_log_files():
    """清理日志文件"""
    print("正在清理日志文件...")
    
    # 清理.log文件
    for log_file in glob.glob("**/*.log", recursive=True):
        print(f"删除: {log_file}")
        os.remove(log_file)

def clean_temp_files():
    """清理临时文件"""
    print("正在清理临时文件...")
    
    # 清理临时文件
    temp_patterns = [
        "**/*.tmp",
        "**/*.temp",
        "**/*~",
        "**/.DS_Store",
        "**/Thumbs.db"
    ]
    
    for pattern in temp_patterns:
        for temp_file in glob.glob(pattern, recursive=True):
            print(f"删除: {temp_file}")
            os.remove(temp_file)

def clean_ide_files():
    """清理IDE配置文件"""
    print("正在清理IDE配置文件...")
    
    # 清理IDE目录
    ide_dirs = [
        ".vscode",
        ".idea",
        ".vs"
    ]
    
    for ide_dir in ide_dirs:
        if os.path.exists(ide_dir):
            print(f"删除: {ide_dir}")
            shutil.rmtree(ide_dir, ignore_errors=True)

def clean_git_files():
    """清理Git文件（可选）"""
    print("检查Git文件...")
    
    if os.path.exists(".git"):
        response = input("是否删除.git目录？(y/N): ")
        if response.lower() == 'y':
            print("删除: .git")
            shutil.rmtree(".git", ignore_errors=True)
        else:
            print("保留.git目录")

def create_source_readme():
    """创建源代码说明文件"""
    readme_content = """# 基于区块链的碳排放核查系统 - 源代码

## 系统概述

本系统是一个基于区块链技术的碳排放核查系统，采用Flask后端、HTML/CSS/JavaScript前端和MySQL数据库的技术架构。系统集成了以太坊区块链技术，确保关键数据的不可篡改性和透明性。

## 技术栈

- **后端**: Python 3.8+, Flask 3.0.0
- **前端**: HTML5, CSS3, JavaScript, Bootstrap
- **数据库**: MySQL 8.0
- **区块链**: 以太坊, Solidity, Web3.py
- **开发工具**: Ganache (本地区块链)

## 目录结构

```
├── backend/                    # 后端代码
│   ├── __init__.py            # Flask应用初始化
│   ├── models/                # 数据库模型
│   ├── routes/                # API路由
│   ├── utils/                 # 工具类
│   └── blockchain/            # 区块链客户端
├── frontend/                  # 前端代码（React组件）
├── templates/                 # HTML模板
├── static/                    # 静态资源
│   ├── css/                   # 样式文件
│   ├── js/                    # JavaScript文件
│   └── images/                # 图片资源
├── blockchain/                # 区块链相关
│   ├── contracts/             # 智能合约
│   └── deploy_contract.py     # 合约部署脚本
├── tests/                     # 测试代码
├── requirements.txt           # Python依赖
├── run.py                     # 应用启动脚本
├── init_db.py                # 数据库初始化
├── create_tables.sql          # 数据库表结构
├── generate_demo_data.py      # 演示数据生成
├── README.md                  # 系统说明
└── DEMO_GUIDE.md             # 演示指南
```

## 核心功能

1. **用户管理**: 支持管理员、企业用户、核查机构三种角色
2. **排放数据管理**: 企业提交、查询、修改排放数据
3. **核查管理**: 核查任务分配、过程记录、结果提交
4. **碳交易**: 企业间碳配额交易
5. **碳计算器**: 多种排放源的碳排放计算
6. **预测分析**: 基于历史数据的趋势分析
7. **报告生成**: 自动生成各类报告

## 区块链集成

系统使用以太坊区块链存储关键数据：
- 排放数据提交记录
- 核查结果验证
- 碳配额交易记录
- 惩罚机制执行

## 安装和运行

### 1. 环境要求
- Python 3.8+
- MySQL 8.0+
- Node.js 14+ (如果需要前端构建)
- Ganache (区块链开发环境)

### 2. 安装依赖
```bash
pip install -r requirements.txt
```

### 3. 数据库配置
```bash
# 创建数据库
mysql -u root -p -e "CREATE DATABASE ces;"

# 导入表结构
mysql -u root -p ces < create_tables.sql

# 初始化数据
python init_db.py
```

### 4. 区块链配置
1. 启动Ganache
2. 部署智能合约：
```bash
python blockchain/deploy_contract.py
```

### 5. 启动系统
```bash
python run.py
```

系统将在 http://localhost:5000 启动

## 测试账户

- **管理员**: admin / admin123
- **企业用户**: enterprise1 / password123
- **核查机构**: verifier1 / password123

## 开发说明

### 代码规范
- 使用PEP 8 Python代码规范
- 函数和类需要添加文档字符串
- 重要功能需要添加注释

### 数据库操作
- 使用SQLAlchemy ORM
- 所有数据库操作需要异常处理
- 敏感操作需要事务支持

### 区块链交互
- 使用Web3.py库与以太坊交互
- 支持模拟模式（区块链不可用时）
- 所有区块链操作需要错误处理

## 部署说明

### 生产环境部署
1. 配置生产数据库
2. 设置环境变量
3. 配置Web服务器（如Nginx）
4. 使用WSGI服务器（如Gunicorn）

### 安全注意事项
- 修改默认密码
- 配置HTTPS
- 设置防火墙规则
- 定期备份数据

## 许可证

本项目仅用于学术研究和教育目的。

## 联系信息

如有问题，请联系开发者。
"""
    
    with open("SOURCE_CODE_README.md", 'w', encoding='utf-8') as f:
        f.write(readme_content)
    
    print("源代码说明文件已创建: SOURCE_CODE_README.md")

def main():
    """主函数"""
    print("=" * 50)
    print("源代码清理工具")
    print("=" * 50)
    
    # 确认操作
    response = input("是否开始清理源代码？这将删除缓存和临时文件 (y/N): ")
    if response.lower() != 'y':
        print("操作已取消")
        return
    
    # 执行清理
    clean_python_cache()
    clean_node_modules()
    clean_build_files()
    clean_log_files()
    clean_temp_files()
    clean_ide_files()
    clean_git_files()
    
    # 创建说明文件
    create_source_readme()
    
    print("=" * 50)
    print("源代码清理完成！")
    print("建议检查以下内容：")
    print("1. 确保所有必要的文件都存在")
    print("2. 验证系统可以正常启动")
    print("3. 检查代码注释是否完整")
    print("4. 确认敏感信息已移除")
    print("=" * 50)

if __name__ == "__main__":
    main()
