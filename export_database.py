#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
数据库导出脚本
用于导出数据库结构和数据，供毕业论文提交使用
"""

import os
import sys
import subprocess
from datetime import datetime

# 数据库配置
DB_CONFIG = {
    'host': '***********',
    'port': '3306',
    'user': 'wuhong',
    'password': 'wuhong',  # 请根据实际情况修改
    'database': 'ces'
}

def export_database_structure():
    """导出数据库结构"""
    print("正在导出数据库结构...")
    
    # 创建导出目录
    export_dir = "database_export"
    if not os.path.exists(export_dir):
        os.makedirs(export_dir)
    
    # 导出数据库结构（不包含数据）
    structure_file = os.path.join(export_dir, "database_structure.sql")
    structure_cmd = [
        'mysqldump',
        f'--host={DB_CONFIG["host"]}',
        f'--port={DB_CONFIG["port"]}',
        f'--user={DB_CONFIG["user"]}',
        f'--password={DB_CONFIG["password"]}',
        '--no-data',  # 只导出结构，不导出数据
        '--routines',  # 导出存储过程和函数
        '--triggers',  # 导出触发器
        DB_CONFIG['database']
    ]
    
    try:
        with open(structure_file, 'w', encoding='utf-8') as f:
            result = subprocess.run(structure_cmd, stdout=f, stderr=subprocess.PIPE, text=True)
            if result.returncode != 0:
                print(f"导出数据库结构失败: {result.stderr}")
                return False
        print(f"数据库结构已导出到: {structure_file}")
        return True
    except Exception as e:
        print(f"导出数据库结构时发生错误: {e}")
        return False

def export_database_data():
    """导出数据库数据"""
    print("正在导出数据库数据...")
    
    export_dir = "database_export"
    if not os.path.exists(export_dir):
        os.makedirs(export_dir)
    
    # 导出数据库数据
    data_file = os.path.join(export_dir, "database_data.sql")
    data_cmd = [
        'mysqldump',
        f'--host={DB_CONFIG["host"]}',
        f'--port={DB_CONFIG["port"]}',
        f'--user={DB_CONFIG["user"]}',
        f'--password={DB_CONFIG["password"]}',
        '--no-create-info',  # 只导出数据，不导出结构
        '--complete-insert',  # 使用完整的INSERT语句
        '--single-transaction',  # 使用单个事务
        DB_CONFIG['database']
    ]
    
    try:
        with open(data_file, 'w', encoding='utf-8') as f:
            result = subprocess.run(data_cmd, stdout=f, stderr=subprocess.PIPE, text=True)
            if result.returncode != 0:
                print(f"导出数据库数据失败: {result.stderr}")
                return False
        print(f"数据库数据已导出到: {data_file}")
        return True
    except Exception as e:
        print(f"导出数据库数据时发生错误: {e}")
        return False

def export_complete_database():
    """导出完整数据库（结构+数据）"""
    print("正在导出完整数据库...")
    
    export_dir = "database_export"
    if not os.path.exists(export_dir):
        os.makedirs(export_dir)
    
    # 导出完整数据库
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    complete_file = os.path.join(export_dir, f"ces_complete_backup_{timestamp}.sql")
    complete_cmd = [
        'mysqldump',
        f'--host={DB_CONFIG["host"]}',
        f'--port={DB_CONFIG["port"]}',
        f'--user={DB_CONFIG["user"]}',
        f'--password={DB_CONFIG["password"]}',
        '--routines',  # 导出存储过程和函数
        '--triggers',  # 导出触发器
        '--single-transaction',  # 使用单个事务
        '--complete-insert',  # 使用完整的INSERT语句
        '--add-drop-table',  # 添加DROP TABLE语句
        DB_CONFIG['database']
    ]
    
    try:
        with open(complete_file, 'w', encoding='utf-8') as f:
            result = subprocess.run(complete_cmd, stdout=f, stderr=subprocess.PIPE, text=True)
            if result.returncode != 0:
                print(f"导出完整数据库失败: {result.stderr}")
                return False
        print(f"完整数据库已导出到: {complete_file}")
        return True
    except Exception as e:
        print(f"导出完整数据库时发生错误: {e}")
        return False

def create_database_readme():
    """创建数据库说明文件"""
    export_dir = "database_export"
    if not os.path.exists(export_dir):
        os.makedirs(export_dir)
    
    readme_content = f"""# 数据库文件说明

## 文件列表

1. **database_structure.sql** - 数据库结构文件
   - 包含所有表的创建语句
   - 包含索引、约束等定义
   - 不包含数据

2. **database_data.sql** - 数据库数据文件
   - 包含所有表的数据
   - 使用INSERT语句格式
   - 不包含表结构

3. **ces_complete_backup_[timestamp].sql** - 完整数据库备份
   - 包含表结构和数据
   - 可直接用于数据库恢复
   - 包含存储过程、触发器等

## 数据库配置信息

- **数据库名称**: {DB_CONFIG['database']}
- **主机地址**: {DB_CONFIG['host']}
- **端口**: {DB_CONFIG['port']}
- **用户名**: {DB_CONFIG['user']}

## 数据库表结构

系统包含以下主要数据表：

1. **users** - 用户表
   - 存储系统用户信息（管理员、企业、核查机构）

2. **emission_data** - 排放数据表
   - 存储企业提交的碳排放数据

3. **verifications** - 核查记录表
   - 存储核查机构的核查结果

4. **transactions** - 交易记录表
   - 存储碳配额交易记录

5. **carbon_quotas** - 碳配额表
   - 存储企业的碳排放配额

6. **penalties** - 惩罚记录表
   - 存储对企业的惩罚记录

## 使用说明

### 恢复数据库结构
```sql
mysql -h {DB_CONFIG['host']} -P {DB_CONFIG['port']} -u {DB_CONFIG['user']} -p {DB_CONFIG['database']} < database_structure.sql
```

### 导入数据
```sql
mysql -h {DB_CONFIG['host']} -P {DB_CONFIG['port']} -u {DB_CONFIG['user']} -p {DB_CONFIG['database']} < database_data.sql
```

### 完整恢复
```sql
mysql -h {DB_CONFIG['host']} -P {DB_CONFIG['port']} -u {DB_CONFIG['user']} -p < ces_complete_backup_[timestamp].sql
```

## 注意事项

1. 导入前请确保目标数据库已创建
2. 如果是完整恢复，请确保数据库名称正确
3. 请根据实际情况修改数据库连接参数
4. 建议在导入前备份现有数据

## 导出时间
{datetime.now().strftime("%Y-%m-%d %H:%M:%S")}
"""
    
    readme_file = os.path.join(export_dir, "README.md")
    with open(readme_file, 'w', encoding='utf-8') as f:
        f.write(readme_content)
    
    print(f"数据库说明文件已创建: {readme_file}")

def main():
    """主函数"""
    print("=" * 50)
    print("数据库导出工具")
    print("=" * 50)
    
    # 检查mysqldump是否可用
    try:
        subprocess.run(['mysqldump', '--version'], capture_output=True, check=True)
    except (subprocess.CalledProcessError, FileNotFoundError):
        print("错误: 未找到mysqldump工具，请确保MySQL客户端已安装")
        return
    
    success_count = 0
    
    # 导出数据库结构
    if export_database_structure():
        success_count += 1
    
    # 导出数据库数据
    if export_database_data():
        success_count += 1
    
    # 导出完整数据库
    if export_complete_database():
        success_count += 1
    
    # 创建说明文件
    create_database_readme()
    
    print("=" * 50)
    print(f"导出完成，成功导出 {success_count}/3 个文件")
    print("导出文件位于 database_export/ 目录")
    print("=" * 50)

if __name__ == "__main__":
    main()
