#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
毕业论文提交文件打包脚本
按照学校要求整理和打包所有提交文件
"""

import os
import shutil
import zipfile
from datetime import datetime
from pathlib import Path

# 配置信息 - 请根据实际情况修改
STUDENT_INFO = {
    'name': '姓名',  # 请修改为实际姓名
    'student_id': '学号',  # 请修改为实际学号
    'title': '基于区块链的碳排放核查系统的设计与实现'
}

def create_directory_structure():
    """创建提交文件目录结构"""
    base_name = f"{STUDENT_INFO['name']}-{STUDENT_INFO['student_id']}-{STUDENT_INFO['title']}-最终版"
    base_dir = Path(base_name)
    
    # 创建主目录
    if base_dir.exists():
        shutil.rmtree(base_dir)
    base_dir.mkdir()
    
    # 创建子目录
    subdirs = [
        "论文文档",
        "程序源代码", 
        "数据库文件",
        "说明文档"
    ]
    
    for subdir in subdirs:
        (base_dir / subdir).mkdir()
    
    print(f"已创建目录结构: {base_dir}")
    return base_dir

def copy_thesis_documents(base_dir):
    """复制论文文档"""
    print("正在复制论文文档...")
    thesis_dir = base_dir / "论文文档"
    
    # 复制Word格式论文
    if os.path.exists("毕业设计2.0.docx"):
        shutil.copy2("毕业设计2.0.docx", thesis_dir)
        print("已复制: 毕业设计2.0.docx")
    
    # 复制Markdown格式论文
    if os.path.exists("基于区块链的碳排放核查系统的设计与实现_完整论文.md"):
        shutil.copy2("基于区块链的碳排放核查系统的设计与实现_完整论文.md", thesis_dir)
        print("已复制: 基于区块链的碳排放核查系统的设计与实现_完整论文.md")
    
    # 复制各章节文件
    chapter_files = [
        "第一章_绪论.md",
        "第二章_相关技术介绍.md", 
        "第三章_系统需求分析.md",
        "第四章_系统设计.md",
        "第五章_系统实现.md",
        "第六章_系统测试.md",
        "第七章_总结与展望.md"
    ]
    
    for chapter_file in chapter_files:
        if os.path.exists(chapter_file):
            shutil.copy2(chapter_file, thesis_dir)
            print(f"已复制: {chapter_file}")

def copy_source_code(base_dir):
    """复制程序源代码"""
    print("正在复制程序源代码...")
    source_dir = base_dir / "程序源代码"
    
    # 需要复制的目录
    dirs_to_copy = [
        "backend",
        "frontend", 
        "templates",
        "static",
        "blockchain",
        "tests"
    ]
    
    for dir_name in dirs_to_copy:
        if os.path.exists(dir_name):
            shutil.copytree(dir_name, source_dir / dir_name)
            print(f"已复制目录: {dir_name}")
    
    # 需要复制的文件
    files_to_copy = [
        "run.py",
        "init_db.py",
        "generate_demo_data.py",
        "requirements.txt",
        "create_tables.sql",
        "README.md",
        "DEMO_GUIDE.md"
    ]
    
    for file_name in files_to_copy:
        if os.path.exists(file_name):
            shutil.copy2(file_name, source_dir)
            print(f"已复制文件: {file_name}")
    
    # 复制源代码说明文件
    if os.path.exists("SOURCE_CODE_README.md"):
        shutil.copy2("SOURCE_CODE_README.md", source_dir / "README.md")
        print("已复制: SOURCE_CODE_README.md")

def copy_database_files(base_dir):
    """复制数据库文件"""
    print("正在复制数据库文件...")
    db_dir = base_dir / "数据库文件"
    
    # 复制数据库导出文件
    if os.path.exists("database_export"):
        for file_name in os.listdir("database_export"):
            src_file = os.path.join("database_export", file_name)
            if os.path.isfile(src_file):
                shutil.copy2(src_file, db_dir)
                print(f"已复制: {file_name}")
    
    # 复制原始SQL文件
    sql_files = [
        "create_tables.sql",
        "init_test_data.sql"
    ]
    
    for sql_file in sql_files:
        if os.path.exists(sql_file):
            shutil.copy2(sql_file, db_dir)
            print(f"已复制: {sql_file}")
        elif os.path.exists(f"tests/{sql_file}"):
            shutil.copy2(f"tests/{sql_file}", db_dir)
            print(f"已复制: tests/{sql_file}")

def copy_documentation(base_dir):
    """复制说明文档"""
    print("正在复制说明文档...")
    doc_dir = base_dir / "说明文档"
    
    # 复制说明文档
    doc_files = [
        "提交文件准备说明.md",
        "系统架构图.md",
        "功能演示说明.md"
    ]
    
    for doc_file in doc_files:
        if os.path.exists(doc_file):
            shutil.copy2(doc_file, doc_dir)
            print(f"已复制: {doc_file}")
    
    # 创建系统部署说明
    deployment_guide = """# 系统部署说明

## 环境要求

- Python 3.8+
- MySQL 8.0+
- Node.js 14+ (可选)
- Ganache (区块链开发环境)

## 部署步骤

### 1. 安装Python依赖
```bash
pip install -r requirements.txt
```

### 2. 配置数据库
1. 创建数据库：
```sql
CREATE DATABASE ces CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
```

2. 导入表结构：
```bash
mysql -u root -p ces < create_tables.sql
```

3. 初始化数据：
```bash
python init_db.py
```

### 3. 配置区块链环境
1. 安装并启动Ganache
2. 部署智能合约：
```bash
python blockchain/deploy_contract.py
```

### 4. 启动系统
```bash
python run.py
```

系统将在 http://localhost:5000 启动

## 测试账户

- 管理员: admin / admin123
- 企业用户: enterprise1 / password123  
- 核查机构: verifier1 / password123

## 注意事项

1. 确保所有依赖已正确安装
2. 数据库连接配置正确
3. Ganache正常运行
4. 防火墙允许相应端口访问
"""
    
    with open(doc_dir / "系统部署说明.md", 'w', encoding='utf-8') as f:
        f.write(deployment_guide)
    print("已创建: 系统部署说明.md")

def create_submission_readme(base_dir):
    """创建提交说明文件"""
    readme_content = f"""# 毕业论文提交文件说明

## 学生信息
- **姓名**: {STUDENT_INFO['name']}
- **学号**: {STUDENT_INFO['student_id']}
- **论文题目**: {STUDENT_INFO['title']}

## 提交文件结构

```
{base_dir.name}/
├── 论文文档/
│   ├── 毕业设计2.0.docx                    # WORD格式论文
│   └── 基于区块链的碳排放核查系统的设计与实现_完整论文.md
├── 程序源代码/
│   ├── backend/                           # 后端代码
│   ├── frontend/                          # 前端代码
│   ├── blockchain/                        # 区块链代码
│   ├── templates/                         # 模板文件
│   ├── static/                           # 静态资源
│   ├── tests/                            # 测试代码
│   ├── requirements.txt                   # 依赖列表
│   ├── run.py                            # 启动脚本
│   └── README.md                         # 源代码说明
├── 数据库文件/
│   ├── database_structure.sql            # 数据库结构
│   ├── database_data.sql                 # 数据库数据
│   ├── ces_complete_backup_*.sql         # 完整备份
│   └── README.md                         # 数据库说明
└── 说明文档/
    ├── 系统部署说明.md                    # 部署指南
    ├── 功能演示说明.md                    # 演示说明
    └── 提交文件准备说明.md                # 文件说明
```

## 系统概述

本系统是一个基于区块链技术的碳排放核查系统，主要功能包括：

1. 用户管理（管理员、企业、核查机构）
2. 排放数据管理
3. 核查流程管理
4. 碳配额交易
5. 碳排放计算
6. 数据分析与报告生成

## 技术特点

- 采用区块链技术确保数据不可篡改
- 支持智能合约自动执行业务逻辑
- 提供完整的Web界面
- 集成多种数据可视化功能

## 创新点

1. 将区块链技术应用于碳排放核查领域
2. 实现了透明、可信的数据管理
3. 提供了完整的碳排放管理解决方案
4. 支持多角色协同工作

## 文件完整性检查

请确认以下文件已包含在提交包中：
- [x] WORD格式论文
- [x] 程序源代码
- [x] 数据库文件
- [x] 系统说明文档

## 联系信息

如有问题，请联系：
- 学生：{STUDENT_INFO['name']}
- 学号：{STUDENT_INFO['student_id']}

---
打包时间：{datetime.now().strftime("%Y-%m-%d %H:%M:%S")}
"""
    
    with open(base_dir / "README.md", 'w', encoding='utf-8') as f:
        f.write(readme_content)
    print("已创建: README.md")

def create_zip_package(base_dir):
    """创建ZIP压缩包"""
    print("正在创建ZIP压缩包...")
    
    zip_filename = f"{base_dir.name}.zip"
    
    with zipfile.ZipFile(zip_filename, 'w', zipfile.ZIP_DEFLATED) as zipf:
        for root, dirs, files in os.walk(base_dir):
            for file in files:
                file_path = os.path.join(root, file)
                arc_name = os.path.relpath(file_path, os.path.dirname(base_dir))
                zipf.write(file_path, arc_name)
                print(f"已添加到压缩包: {arc_name}")
    
    print(f"压缩包已创建: {zip_filename}")
    
    # 显示压缩包信息
    zip_size = os.path.getsize(zip_filename) / (1024 * 1024)  # MB
    print(f"压缩包大小: {zip_size:.2f} MB")
    
    return zip_filename

def main():
    """主函数"""
    print("=" * 60)
    print("毕业论文提交文件打包工具")
    print("=" * 60)
    
    # 检查学生信息
    if STUDENT_INFO['name'] == '姓名' or STUDENT_INFO['student_id'] == '学号':
        print("警告: 请先在脚本中修改学生信息（姓名和学号）")
        response = input("是否继续？(y/N): ")
        if response.lower() != 'y':
            return
    
    print(f"学生姓名: {STUDENT_INFO['name']}")
    print(f"学号: {STUDENT_INFO['student_id']}")
    print(f"论文题目: {STUDENT_INFO['title']}")
    print()
    
    # 确认操作
    response = input("是否开始打包提交文件？(y/N): ")
    if response.lower() != 'y':
        print("操作已取消")
        return
    
    try:
        # 创建目录结构
        base_dir = create_directory_structure()
        
        # 复制各类文件
        copy_thesis_documents(base_dir)
        copy_source_code(base_dir)
        copy_database_files(base_dir)
        copy_documentation(base_dir)
        
        # 创建说明文件
        create_submission_readme(base_dir)
        
        # 创建压缩包
        zip_filename = create_zip_package(base_dir)
        
        print("=" * 60)
        print("打包完成！")
        print(f"提交文件夹: {base_dir}")
        print(f"压缩包: {zip_filename}")
        print()
        print("下一步操作：")
        print("1. 将WORD论文转换为PDF格式")
        print("2. 按照学校要求命名PDF文件")
        print("3. 检查压缩包内容完整性")
        print("4. 提交PDF论文和压缩包")
        print("=" * 60)
        
    except Exception as e:
        print(f"打包过程中发生错误: {e}")
        print("请检查文件是否存在，然后重试")

if __name__ == "__main__":
    main()
